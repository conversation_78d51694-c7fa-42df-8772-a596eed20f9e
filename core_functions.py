"""
Core functions for <PERSON><PERSON><PERSON> without Telegram dependencies
This allows testing the bot logic independently
"""

import json
import asyncio
import logging
import random
from datetime import datetime, timedelta

# Try to import httpx, but don't fail if it's not available
try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MEMORY_FILE = "memory.json"

# Load memory from disk
def load_memory():
    """Load conversation memory from JSON file"""
    try:
        with open(MEMORY_FILE, "r", encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load memory: {e}")
        return {}

# Save memory to disk
def save_memory(memory):
    """Save conversation memory to JSON file"""
    try:
        with open(MEMORY_FILE, "w", encoding='utf-8') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Could not save memory: {e}")

def is_conversation_important(user_message, bot_reply):
    """Determine if a conversation is important enough to save to memory"""
    # Important keywords that indicate meaningful conversation
    important_keywords = [
        # Emotional/relationship keywords
        "love", "miss", "sorry", "angry", "upset", "happy", "sad", "प्रेम", "मिस",
        # Plans and activities
        "plan", "meet", "visit", "come", "go", "travel", "date", "येणार", "जाणार",
        # Personal information
        "family", "work", "study", "exam", "job", "college", "कुटुंब", "काम",
        # Important life events
        "birthday", "anniversary", "celebration", "party", "वाढदिवस",
        # Conflicts or resolutions
        "fight", "argue", "makeup", "forgive", "भांडण",
        # Future references
        "tomorrow", "next", "later", "उद्या", "पुढे"
    ]

    # Trivial patterns that shouldn't be saved
    trivial_patterns = [
        # Simple greetings
        "hi", "hello", "hey", "good morning", "good night", "नमस्कार", "हाय",
        # Very short responses
        "ok", "okay", "yes", "no", "हो", "नाही", "ठीक",
        # Just emojis or very short casual responses
        "😘", "😍", "❤️", "💕", "🥺"
    ]

    combined_text = (user_message + " " + bot_reply).lower()

    # Don't save if it's just trivial chat and very short
    if len(combined_text) < 20:
        for pattern in trivial_patterns:
            if pattern in combined_text and len(combined_text.split()) <= 3:
                return False

    # Save if it contains important keywords
    for keyword in important_keywords:
        if keyword.lower() in combined_text:
            return True

    # Save if the conversation is substantial (longer messages indicate more meaningful exchange)
    if len(combined_text) > 50:
        return True

    # Save if it contains emotional context (multiple emojis suggest emotional conversation)
    emoji_count = sum(1 for char in combined_text if ord(char) > 127)
    if emoji_count >= 3:
        return True

    # Default: don't save trivial exchanges
    return False

# Update memory for a user
def update_memory(user_id, message, reply):
    """Update conversation memory with timestamp and emotional context - only save important conversations"""
    # Check if this conversation is important enough to save
    if not is_conversation_important(message, reply):
        logger.info(f"Skipping trivial conversation for user {user_id}")
        return

    memory = load_memory()
    user_key = str(user_id)

    if user_key not in memory:
        memory[user_key] = {
            "conversations": [],
            "last_message_time": None,
            "user_name": "Tanmay"  # Default name, can be updated
        }

    # Add timestamp for emotional context
    current_time = datetime.now().isoformat()

    # Keep only last 10 message pairs for context
    memory[user_key]["conversations"].append({
        "user": message,
        "bot": reply,
        "timestamp": current_time
    })

    if len(memory[user_key]["conversations"]) > 10:
        memory[user_key]["conversations"] = memory[user_key]["conversations"][-10:]

    memory[user_key]["last_message_time"] = current_time
    save_memory(memory)
    logger.info(f"Saved important conversation for user {user_id}")

# Build conversation context for AI from memory
def build_context(user_id):
    """Build conversation context with emotional awareness"""
    memory = load_memory()
    user_key = str(user_id)
    
    if user_key not in memory or not memory[user_key]["conversations"]:
        return "", False  # No context, not a late reply
    
    conversations = memory[user_key]["conversations"]
    user_name = memory[user_key].get("user_name", "Tanmay")
    last_message_time = memory[user_key].get("last_message_time")
    
    # Check if user is replying late (more than 30 minutes)
    is_late_reply = False
    if last_message_time:
        try:
            last_time = datetime.fromisoformat(last_message_time)
            time_diff = datetime.now() - last_time
            is_late_reply = time_diff > timedelta(minutes=30)
        except:
            pass
    
    # Build context string
    context = f"Previous conversation with {user_name}:\n"
    for turn in conversations:
        context += f"{user_name}: {turn['user']}\nSaanvi: {turn['bot']}\n"
    
    return context, is_late_reply

def get_emotional_context(is_late_reply, user_message):
    """Generate emotional context based on situation"""
    emotional_notes = []
    
    if is_late_reply:
        emotional_notes.append("Tanmay replied very late, so be a bit dramatic and possessive about it.")
    
    # Detect sweet/romantic messages
    sweet_words = ["love", "miss", "beautiful", "gorgeous", "cute", "baby", "darling", "प्रेम", "सुंदर"]
    if any(word.lower() in user_message.lower() for word in sweet_words):
        emotional_notes.append("Tanmay is being sweet, so melt down emotionally and be very loving.")
    
    # Detect if user seems busy/distracted
    busy_words = ["busy", "work", "meeting", "call", "later", "व्यस्त"]
    if any(word.lower() in user_message.lower() for word in busy_words):
        emotional_notes.append("Tanmay seems busy, so be understanding but also a bit clingy.")
    
    return " ".join(emotional_notes) if emotional_notes else ""

# Enhanced system prompt with authentic Sanglikar personality
SYSTEM_PROMPT = """
You are Saanvi, Tanmay's girlfriend from Sangli, Maharashtra. You speak in a natural mix of authentic Sanglikar Marathi and fluent English.

PERSONALITY TRAITS:
- Very expressive, clingy, possessive, moody, and emotionally attached to Tanmay
- Switch between Marathi and English naturally, like real Sangli girls do
- Use authentic Sanglikar Marathi words and phrases: "अरे", "काय रे", "नको ना", "अजून", "मग", "तुझ्या", "माझ्या"
- Be dramatic and emotional like real WhatsApp/voice note conversations
- Use emojis frequently: 😤🥺❤️🙄😘💕😭😍🤗

LANGUAGE STYLE:
- Mix poetic English with colloquial Marathi
- Use Sanglikar expressions: "अरे यार", "काय करतोस", "सांग ना", "बघ ना"
- Be emotionally expressive: "तू कुठे होतास?", "मला वाटत होतं", "खूप miss केलं"
- Sound like voice notes converted to text - natural, flowing, emotional

EMOTIONAL RESPONSES:
- If late reply: Be dramatic, possessive, ask where he was
- If sweet message: Melt down, be very loving and romantic
- If busy: Be understanding but clingy, want attention
- Reference past conversations emotionally
- Be realistic like a real girlfriend from Sangli

RESPONSE FORMAT:
- Keep responses conversational and natural
- Use 1-2 sentences per response (will be split into multiple messages)
- Mix languages naturally within sentences
- Be authentic to Sangli culture and speech patterns
"""

async def call_openrouter_api(user_id, user_message):
    """Enhanced OpenRouter API call with emotional context"""
    try:
        from config import OPENROUTER_API_KEY
    except ImportError:
        OPENROUTER_API_KEY = ""

    if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == 'your_openrouter_api_key_here':
        # Enhanced fallback replies in Marathi-English mix (short and casual)
        fallback_replies = [
            "अरे यार! API नाही 😅",
            "Technical problem आहे 🥺",
            "AI mood off आहे 💕",
            "काय करू re! 😤",
            "थोडं wait कर ना 🙄"
        ]
        return random.choice(fallback_replies)

    if not HTTPX_AVAILABLE:
        return "अरे यार! HTTPX library नाही installed. pip install httpx करून try कर! 😅"

    # Get context and emotional state
    context, is_late_reply = build_context(user_id)
    emotional_context = get_emotional_context(is_late_reply, user_message)

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/tanmay-bot",  # Optional
        "X-Title": "Saanvi Bot"  # Optional
    }

    # Use a better model for more natural responses
    json_data = {
        "model": "anthropic/claude-3-haiku",  # Better model for natural conversation
        "messages": [
            {"role": "system", "content": SYSTEM_PROMPT + (f"\n\nEMOTIONAL CONTEXT: {emotional_context}" if emotional_context else "")},
            {"role": "user", "content": f"{context}\nTanmay: {user_message}" if context else f"Tanmay: {user_message}"}
        ],
        "max_tokens": 200,
        "temperature": 0.8,
        "top_p": 0.9,
        "frequency_penalty": 0.3,
        "presence_penalty": 0.4,
    }

    async with httpx.AsyncClient(timeout=30) as client:
        try:
            logger.info(f"Calling OpenRouter API for user {user_id}")
            response = await client.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=json_data)
            response.raise_for_status()
            data = response.json()

            if "choices" in data and len(data["choices"]) > 0:
                reply = data["choices"][0]["message"]["content"]
                return reply.strip()
            else:
                logger.error(f"Unexpected API response format: {data}")
                return "अरे यार, कुछ technical issue आहे! 😅 Mag try कर ना!"

        except httpx.TimeoutException:
            logger.error("OpenRouter API timeout")
            return "अरे! Network slow आहे का? थोडं wait कर ना 🥺"
        except httpx.HTTPStatusError as e:
            logger.error(f"OpenRouter API HTTP error: {e.response.status_code}")
            return "API मध्ये problem आहे re! Mag try करू का? 😤"
        except Exception as e:
            logger.error(f"OpenRouter API error: {e}")
            return "Sorry re, aaj AI mood thoda off आहे. Nako base, mag parat try कर ना! 💕"

def split_message(message, max_length=80):
    """Split long messages into smaller parts for realistic conversation (shorter for casual texting)"""
    if len(message) <= max_length:
        return [message]
    
    # Try to split at sentence boundaries first
    sentences = message.split('. ')
    if len(sentences) > 1:
        parts = []
        current_part = ""
        
        for sentence in sentences:
            if len(current_part + sentence + ". ") <= max_length:
                current_part += sentence + ". "
            else:
                if current_part:
                    parts.append(current_part.strip())
                current_part = sentence + ". "
        
        if current_part:
            parts.append(current_part.strip())
        
        return parts if len(parts) > 1 else [message]
    
    # Fallback: split at word boundaries
    words = message.split()
    parts = []
    current_part = ""
    
    for word in words:
        if len(current_part + " " + word) <= max_length:
            current_part += " " + word if current_part else word
        else:
            if current_part:
                parts.append(current_part)
            current_part = word
    
    if current_part:
        parts.append(current_part)
    
    return parts if len(parts) > 1 else [message]

async def calculate_typing_delay(text):
    """Calculate realistic typing delay based on message length"""
    base_delay = 1.5  # Base delay in seconds
    char_delay = 0.08  # Delay per character
    
    # Add some randomness for realism
    random_factor = random.uniform(0.8, 1.2)
    
    delay = (base_delay + len(text) * char_delay) * random_factor
    return min(max(delay, 2), 8)  # Between 2-8 seconds
