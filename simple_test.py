#!/usr/bin/env python3
"""
Simple test for basic bot functions without any external dependencies
"""

import json
import asyncio
import random
from datetime import datetime, timedelta

# Simple memory functions
def load_memory():
    """Load conversation memory from JSON file"""
    try:
        with open("memory.json", "r", encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}

def save_memory(memory):
    """Save conversation memory to JSON file"""
    try:
        with open("memory.json", "w", encoding='utf-8') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Could not save memory: {e}")

def is_conversation_important(user_message, bot_reply):
    """Determine if a conversation is important enough to save to memory"""
    # Important keywords that indicate meaningful conversation
    important_keywords = [
        # Emotional/relationship keywords
        "love", "miss", "sorry", "angry", "upset", "happy", "sad", "प्रेम", "मिस",
        # Plans and activities
        "plan", "meet", "visit", "come", "go", "travel", "date", "येणार", "जाणार",
        # Personal information
        "family", "work", "study", "exam", "job", "college", "कुटुंब", "काम",
        # Important life events
        "birthday", "anniversary", "celebration", "party", "वाढदिवस",
        # Conflicts or resolutions
        "fight", "argue", "makeup", "forgive", "भांडण",
        # Future references
        "tomorrow", "next", "later", "उद्या", "पुढे"
    ]

    # Trivial patterns that shouldn't be saved
    trivial_patterns = [
        # Simple greetings
        "hi", "hello", "hey", "good morning", "good night", "नमस्कार", "हाय",
        # Very short responses
        "ok", "okay", "yes", "no", "हो", "नाही", "ठीक",
        # Just emojis or very short casual responses
        "😘", "😍", "❤️", "💕", "🥺"
    ]

    combined_text = (user_message + " " + bot_reply).lower()

    # Don't save if it's just trivial chat and very short
    if len(combined_text) < 20:
        for pattern in trivial_patterns:
            if pattern in combined_text and len(combined_text.split()) <= 3:
                return False

    # Save if it contains important keywords
    for keyword in important_keywords:
        if keyword.lower() in combined_text:
            return True

    # Save if the conversation is substantial (longer messages indicate more meaningful exchange)
    if len(combined_text) > 50:
        return True

    # Save if it contains emotional context (multiple emojis suggest emotional conversation)
    emoji_count = sum(1 for char in combined_text if ord(char) > 127)
    if emoji_count >= 3:
        return True

    # Default: don't save trivial exchanges
    return False

def update_memory(user_id, message, reply):
    """Update conversation memory with timestamp - only save important conversations"""
    # Check if this conversation is important enough to save
    if not is_conversation_important(message, reply):
        print(f"Skipping trivial conversation for user {user_id}")
        return

    memory = load_memory()
    user_key = str(user_id)

    if user_key not in memory:
        memory[user_key] = {
            "conversations": [],
            "last_message_time": None,
            "user_name": "Tanmay"
        }

    current_time = datetime.now().isoformat()

    memory[user_key]["conversations"].append({
        "user": message,
        "bot": reply,
        "timestamp": current_time
    })

    if len(memory[user_key]["conversations"]) > 10:
        memory[user_key]["conversations"] = memory[user_key]["conversations"][-10:]

    memory[user_key]["last_message_time"] = current_time
    save_memory(memory)
    print(f"Saved important conversation for user {user_id}")

def get_emotional_context(is_late_reply, user_message):
    """Generate emotional context based on situation"""
    emotional_notes = []
    
    if is_late_reply:
        emotional_notes.append("Tanmay replied very late, so be a bit dramatic and possessive about it.")
    
    sweet_words = ["love", "miss", "beautiful", "gorgeous", "cute", "baby", "darling", "प्रेम", "सुंदर"]
    if any(word.lower() in user_message.lower() for word in sweet_words):
        emotional_notes.append("Tanmay is being sweet, so melt down emotionally and be very loving.")
    
    busy_words = ["busy", "work", "meeting", "call", "later", "व्यस्त"]
    if any(word.lower() in user_message.lower() for word in busy_words):
        emotional_notes.append("Tanmay seems busy, so be understanding but also a bit clingy.")
    
    return " ".join(emotional_notes) if emotional_notes else ""

def split_message(message, max_length=80):
    """Split long messages into smaller parts (shorter for casual texting)"""
    if len(message) <= max_length:
        return [message]
    
    sentences = message.split('. ')
    if len(sentences) > 1:
        parts = []
        current_part = ""
        
        for sentence in sentences:
            if len(current_part + sentence + ". ") <= max_length:
                current_part += sentence + ". "
            else:
                if current_part:
                    parts.append(current_part.strip())
                current_part = sentence + ". "
        
        if current_part:
            parts.append(current_part.strip())
        
        return parts if len(parts) > 1 else [message]
    
    words = message.split()
    parts = []
    current_part = ""
    
    for word in words:
        if len(current_part + " " + word) <= max_length:
            current_part += " " + word if current_part else word
        else:
            if current_part:
                parts.append(current_part)
            current_part = word
    
    if current_part:
        parts.append(current_part)
    
    return parts if len(parts) > 1 else [message]

async def calculate_typing_delay(text):
    """Calculate realistic typing delay"""
    base_delay = 1.5
    char_delay = 0.08
    random_factor = random.uniform(0.8, 1.2)
    delay = (base_delay + len(text) * char_delay) * random_factor
    return min(max(delay, 2), 8)

def get_fallback_reply():
    """Get a fallback reply in Marathi-English mix"""
    fallback_replies = [
        "अरे यार! API key नाही ना, मग कसं बोलू? 😅 Tula manually reply करावं लागेल!",
        "Hey re! Technical problem आहे, थोडं wait कर ना 🥺",
        "Aaj AI mood off आहे, पण तू बोल ना, मी ऐकते आहे! 💕",
        "अरे! मला तुझं message समजलं, पण AI थोडं slow आहे आज 😅",
        "काय मhantay तू? मी इथे आहे, बोल ना! 💕"
    ]
    return random.choice(fallback_replies)

def test_all_functions():
    """Test all basic functions"""
    print("🌟 Simple Bot Function Test")
    print("=" * 30)
    
    # Test memory functions
    print("🧠 Testing memory functions...")
    test_user_id = 12345
    test_message = "Hello Saanvi!"
    test_reply = "अरे यार! कसा आहेस? 😘"
    
    try:
        update_memory(test_user_id, test_message, test_reply)
        memory = load_memory()
        print("✅ Memory functions work!")
        print(f"   Stored {len(memory.get(str(test_user_id), {}).get('conversations', []))} conversations")
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False
    
    # Test emotional context
    print("\n💝 Testing emotional context...")
    try:
        context1 = get_emotional_context(False, "I love you baby")
        context2 = get_emotional_context(True, "Sorry, was busy")
        context3 = get_emotional_context(False, "Hey what's up")
        
        print("✅ Emotional context works!")
        print(f"   Sweet message: {context1[:50]}...")
        print(f"   Late + busy: {context2[:50]}...")
        print(f"   Normal: '{context3}'" if context3 else "   Normal: no special context")
    except Exception as e:
        print(f"❌ Emotional context test failed: {e}")
        return False
    
    # Test message splitting
    print("\n✂️ Testing message splitting...")
    try:
        long_message = "This is a very long message that should be split into multiple parts because it exceeds the maximum length limit that we set for realistic conversation flow and natural texting patterns."
        parts = split_message(long_message, max_length=50)
        
        print("✅ Message splitting works!")
        print(f"   Split into {len(parts)} parts:")
        for i, part in enumerate(parts, 1):
            print(f"   Part {i}: {part[:30]}...")
    except Exception as e:
        print(f"❌ Message splitting test failed: {e}")
        return False
    
    # Test typing delay
    print("\n⏱️ Testing typing delay...")
    try:
        async def test_delay():
            delay1 = await calculate_typing_delay("Hi")
            delay2 = await calculate_typing_delay("This is a medium length message")
            delay3 = await calculate_typing_delay("This is a very long message that should take more time")
            
            print("✅ Typing delay works!")
            print(f"   Short message: {delay1:.2f}s")
            print(f"   Medium message: {delay2:.2f}s")
            print(f"   Long message: {delay3:.2f}s")
        
        asyncio.run(test_delay())
    except Exception as e:
        print(f"❌ Typing delay test failed: {e}")
        return False
    
    # Test fallback replies
    print("\n🤖 Testing fallback replies...")
    try:
        reply1 = get_fallback_reply()
        reply2 = get_fallback_reply()
        
        print("✅ Fallback replies work!")
        print(f"   Sample reply 1: {reply1}")
        print(f"   Sample reply 2: {reply2}")
    except Exception as e:
        print(f"❌ Fallback reply test failed: {e}")
        return False
    
    print("\n" + "=" * 30)
    print("🎉 All basic functions work!")
    print("✅ Core bot logic is functional")
    print("\n📝 Next steps:")
    print("   1. Install compatible telegram library")
    print("   2. Run the full bot with: python main.py")
    print("   3. Test with your Telegram bot")
    
    return True

if __name__ == "__main__":
    try:
        success = test_all_functions()
        if success:
            print("\n🌟 Ready to run the full bot!")
        else:
            print("\n⚠️  Some basic functions failed")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
