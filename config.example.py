# Saanvi Bot Configuration
# Copy this file to config.py and add your actual tokens

# Telegram Bot Token (Required)
# Get this from @BotFather on Telegram
TELEGRAM_BOT_TOKEN = 'your_telegram_bot_token_here'

# OpenRouter API Key (Optional but recommended)
# Get this from https://openrouter.ai
# Leave empty string '' if you don't have one (bot will use fallback responses)
OPENROUTER_API_KEY = 'your_openrouter_api_key_here'

# Additional Configuration (Optional)
# You can add more settings here if needed

# Bot Settings
BOT_NAME = "Saanvi"
DEFAULT_USER_NAME = "Tanmay"

# Memory Settings
MAX_MEMORY_CONVERSATIONS = 1000
MEMORY_FILE = "memory.json"

# Timing Settings
MIN_TYPING_DELAY = 6  # seconds
MAX_TYPING_DELAY = 30  # seconds
LATE_REPLY_THRESHOLD = 120  # minutes
