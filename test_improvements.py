#!/usr/bin/env python3
"""
Test script to demonstrate the new improvements:
1. Shorter, more casual message responses
2. Smart memory filtering (only saves important conversations)
"""

import json
from simple_test import is_conversation_important, update_memory, split_message

def test_memory_filtering():
    """Test the new memory filtering system"""
    print("🧠 Testing Smart Memory Filtering")
    print("=" * 40)
    
    # Test cases: (user_message, bot_reply, should_save)
    test_cases = [
        # Trivial conversations (should NOT be saved)
        ("hi", "hey 😘", False),
        ("ok", "ठीक आहे", False),
        ("😍", "💕", False),
        ("good morning", "morning baby", False),
        ("yes", "हो ना", False),
        
        # Important conversations (SHOULD be saved)
        ("I love you so much", "aww मी पण love करते तुला 💕", True),
        ("I miss you", "मी पण miss करते तुला 🥺", True),
        ("Let's meet tomorrow", "हो ना! कुठे भेटायचं? 😍", True),
        ("I'm going to work", "काम वर जातोस? मग मला call कर ना 😤", True),
        ("Sorry I was busy", "अरे! busy होतास का? मला वाटलं ignore करतोस 🥺", True),
        ("My family is visiting", "family येणार आहे? मला पण भेटायला आणशील ना? 😘", True),
        ("I have an exam tomorrow", "exam आहे? मग study कर, मी disturb करणार नाही 💕", True),
    ]
    
    saved_count = 0
    skipped_count = 0
    
    for user_msg, bot_reply, expected in test_cases:
        result = is_conversation_important(user_msg, bot_reply)
        status = "✅ SAVE" if result else "❌ SKIP"
        expected_status = "✅ SAVE" if expected else "❌ SKIP"
        
        print(f"{status} | User: '{user_msg}' | Bot: '{bot_reply}'")
        
        if result == expected:
            print(f"     ✓ Correct decision (Expected: {expected_status})")
        else:
            print(f"     ✗ Wrong decision (Expected: {expected_status})")
        
        if result:
            saved_count += 1
        else:
            skipped_count += 1
        
        print()
    
    print(f"📊 Results: {saved_count} saved, {skipped_count} skipped")
    print(f"💾 Memory efficiency: {skipped_count/(saved_count+skipped_count)*100:.1f}% reduction in storage")

def test_message_splitting():
    """Test the new shorter message splitting"""
    print("\n✂️ Testing Shorter Message Splitting")
    print("=" * 40)
    
    test_messages = [
        "अरे यार! तू कुठे होतास? मी खूप wait केली तुझ्यासाठी 🥺",
        "I love you so much baby! You make me feel so special and happy every single day 💕",
        "काय करतोस आज? College आहे का? मला boring वाटतंय, तू येशील ना? 😘",
        "Hey! 😍",  # Short message
    ]
    
    for msg in test_messages:
        print(f"Original: {msg}")
        parts = split_message(msg, max_length=80)
        print(f"Split into {len(parts)} parts:")
        for i, part in enumerate(parts, 1):
            print(f"  {i}. {part} ({len(part)} chars)")
        print()

def test_realistic_conversation():
    """Test a realistic conversation flow with memory filtering"""
    print("💬 Testing Realistic Conversation Flow")
    print("=" * 40)
    
    user_id = 12345
    
    # Simulate a conversation
    conversations = [
        ("hi", "hey baby 😘"),  # Trivial - should be skipped
        ("how are you?", "good! तू कसा आहेस? 💕"),  # Substantial - should be saved
        ("ok", "ठीक 😊"),  # Trivial - should be skipped
        ("I miss you so much", "aww मी पण miss करते 🥺💕"),  # Important - should be saved
        ("yes", "हो ना 😍"),  # Trivial - should be skipped
        ("let's meet tomorrow", "हो! कुठे भेटायचं? 😘"),  # Important - should be saved
    ]
    
    print("Simulating conversation...")
    for user_msg, bot_reply in conversations:
        print(f"👤 User: {user_msg}")
        print(f"💕 Saanvi: {bot_reply}")
        
        # This will automatically filter and only save important conversations
        update_memory(user_id, user_msg, bot_reply)
        print()
    
    # Check what was actually saved
    try:
        with open("memory.json", "r", encoding='utf-8') as f:
            memory = json.load(f)
            
        if str(user_id) in memory:
            saved_conversations = memory[str(user_id)]["conversations"]
            print(f"📚 Saved {len(saved_conversations)} important conversations:")
            for i, convo in enumerate(saved_conversations, 1):
                print(f"  {i}. User: '{convo['user']}' | Bot: '{convo['bot']}'")
        else:
            print("📚 No conversations saved (all were trivial)")
            
    except Exception as e:
        print(f"Error reading memory: {e}")

if __name__ == "__main__":
    print("🌟 Testing Saanvi Bot Improvements")
    print("=" * 50)
    
    test_memory_filtering()
    test_message_splitting()
    test_realistic_conversation()
    
    print("\n🎉 All tests completed!")
    print("\n✨ Key Improvements:")
    print("  • Messages are now shorter and more casual (3-8 words when possible)")
    print("  • Memory only saves important conversations (emotional, plans, personal info)")
    print("  • Trivial exchanges like 'hi', 'ok', single emojis are filtered out")
    print("  • More natural, spontaneous responses with higher temperature")
    print("  • Shorter message splitting (80 chars vs 120) for realistic texting")
