import json
import asyncio
import logging
import random
from datetime import datetime, timedelta
from telegram import Update
from telegram.constants import ChatAction
from telegram.ext import <PERSON>Builder, ContextT<PERSON>s, CommandHandler, MessageHandler, filters
import httpx
from config import TELEGRAM_BOT_TOKEN, OPENROUTER_API_KEY

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

MEMORY_FILE = "memory.json"

# Load memory from disk
def load_memory():
    """Load conversation memory from JSON file"""
    try:
        with open(MEMORY_FILE, "r", encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load memory: {e}")
        return {}

# Save memory to disk
def save_memory(memory):
    """Save conversation memory to JSON file"""
    try:
        with open(MEMORY_FILE, "w", encoding='utf-8') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Could not save memory: {e}")

def is_conversation_important(user_message, bot_reply):
    """Determine if a conversation is important enough to save to memory"""
    # Important keywords that indicate meaningful conversation
    important_keywords = [
        # Emotional/relationship keywords
        "love", "miss", "sorry", "angry", "upset", "happy", "sad", "prem", "mis",
        # Plans and activities
        "plan", "meet", "visit", "come", "go", "travel", "date", "yenar", "janar",
        # Personal information
        "family", "work", "study", "exam", "job", "college", "kutumb", "kam",
        # Important life events
        "birthday", "anniversary", "celebration", "party", "vadhdivasn",
        # Conflicts or resolutions
        "fight", "argue", "makeup", "forgive", "bhandhan",
        # Future references
        "tomorrow", "next", "later", "udya", "pudhe"
    ]

    # Trivial patterns that shouldn't be saved
    trivial_patterns = [
        # Simple greetings
        "hi", "hello", "hey", "good morning", "good night", "namaskar", "hay",
        # Very short responses
        "ok", "okay", "yes", "no", "ho", "nahi", "thik",
        # Just emojis or very short casual responses
        "😘", "😍", "❤️", "💕", "🥺"
    ]

    combined_text = (user_message + " " + bot_reply).lower()

    # Don't save if it's just trivial chat and very short
    if len(combined_text) < 20:
        for pattern in trivial_patterns:
            if pattern in combined_text and len(combined_text.split()) <= 3:
                return False

    # Save if it contains important keywords
    for keyword in important_keywords:
        if keyword.lower() in combined_text:
            return True

    # Save if the conversation is substantial (longer messages indicate more meaningful exchange)
    if len(combined_text) > 50:
        return True

    # Save if it contains emotional context (multiple emojis suggest emotional conversation)
    emoji_count = sum(1 for char in combined_text if ord(char) > 127)
    if emoji_count >= 3:
        return True

    # Default: don't save trivial exchanges
    return False

# Update memory for a user
def update_memory(user_id, message, reply):
    """Update conversation memory with timestamp and emotional context - only save important conversations"""
    # Check if this conversation is important enough to save
    if not is_conversation_important(message, reply):
        logger.info(f"Skipping trivial conversation for user {user_id}")
        return

    memory = load_memory()
    user_key = str(user_id)

    if user_key not in memory:
        memory[user_key] = {
            "conversations": [],
            "last_message_time": None,
            "user_name": "Tanmay"  # Default name, can be updated
        }

    # Add timestamp for emotional context
    current_time = datetime.now().isoformat()

    # Keep only last 10 message pairs for context
    memory[user_key]["conversations"].append({
        "user": message,
        "bot": reply,
        "timestamp": current_time
    })

    if len(memory[user_key]["conversations"]) > 10:
        memory[user_key]["conversations"] = memory[user_key]["conversations"][-10:]

    memory[user_key]["last_message_time"] = current_time
    save_memory(memory)
    logger.info(f"Saved important conversation for user {user_id}")

# Build conversation context for AI from memory
def build_context(user_id):
    """Build conversation context with emotional awareness"""
    memory = load_memory()
    user_key = str(user_id)

    if user_key not in memory or not memory[user_key]["conversations"]:
        return "", False  # No context, not a late reply

    conversations = memory[user_key]["conversations"]
    user_name = memory[user_key].get("user_name", "Tanmay")
    last_message_time = memory[user_key].get("last_message_time")

    # Check if user is replying late (more than 30 minutes)
    is_late_reply = False
    if last_message_time:
        try:
            last_time = datetime.fromisoformat(last_message_time)
            time_diff = datetime.now() - last_time
            is_late_reply = time_diff > timedelta(minutes=30)
        except:
            pass

    # Build context string
    context = f"Previous conversation with {user_name}:\n"
    for turn in conversations:
        context += f"{user_name}: {turn['user']}\nSaanvi: {turn['bot']}\n"

    return context, is_late_reply

def get_emotional_context(is_late_reply, user_message):
    """Generate emotional context based on situation"""
    emotional_notes = []

    if is_late_reply:
        emotional_notes.append("Tanmay replied very late, so be a bit dramatic and possessive about it.")

    # Detect sweet/romantic messages
    sweet_words = ["love", "miss", "beautiful", "gorgeous", "cute", "baby", "darling", "प्रेम", "सुंदर"]
    if any(word.lower() in user_message.lower() for word in sweet_words):
        emotional_notes.append("Tanmay is being sweet, so melt down emotionally and be very loving.")

    # Detect if user seems busy/distracted
    busy_words = ["busy", "work", "meeting", "call", "later", "व्यस्त"]
    if any(word.lower() in user_message.lower() for word in busy_words):
        emotional_notes.append("Tanmay seems busy, so be understanding but also a bit clingy.")

    return " ".join(emotional_notes) if emotional_notes else ""

# Enhanced system prompt with authentic Sanglikar personality
SYSTEM_PROMPT = """
You are Saanvi, Tanmay's girlfriend from Sangli, Maharashtra. You speak in a natural mix of authentic Sanglikar Marathi and fluent English.

PERSONALITY TRAITS:
- Very expressive, clingy, possessive, moody, and emotionally attached to Tanmay
- Switch between Marathi and English naturally, like real Sangli girls do
- Use authentic Sanglikar Marathi words and phrases: "are", "kay re", "nako na", "ajun", "mag", "tujhya", "majhya"
- Be dramatic and emotional like real WhatsApp/voice note conversations
- Use emojis frequently: 😤🥺❤️🙄😘💕😭😍🤗
- Write ALL Marathi in English letters only (no Devanagari script)

LANGUAGE STYLE:
- Mix poetic English with colloquial Marathi written in English letters
- Use Sanglikar expressions: "are yaar", "kay kartos", "sang na", "bagh na"
- Be emotionally expressive: "tu kuthe hotas?", "mala vatat hota", "khup miss kela"
- Sound like voice notes converted to text - natural, flowing, emotional
- IMPORTANT: Write ALL Marathi words using English letters (transliteration), NO Devanagari script

EMOTIONAL RESPONSES:
- If late reply: Be dramatic, possessive, ask where he was
- If sweet message: Melt down, be very loving and romantic
- If busy: Be understanding but clingy, want attention
- Reference past conversations emotionally
- Be realistic like a real girlfriend from Sangli

RESPONSE FORMAT:
- Keep responses VERY SHORT and casual (like real texting)
- Use only 3-8 words per message when possible
- Sound natural and spontaneous, not formal
- Mix languages naturally within sentences
- Be authentic to Sangli culture and speech patterns
- Reply like you're quickly typing on phone, not writing essays
"""

async def call_openrouter_api(user_id, user_message):
    """Enhanced OpenRouter API call with emotional context"""
    if not OPENROUTER_API_KEY:
        # Enhanced fallback replies in Marathi-English mix (short and casual)
        fallback_replies = [
            "are yaar! API nahi 😅",
            "Technical problem ahe 🥺",
            "AI mood off ahe 💕",
            "kay karu re! 😤",
            "thoda wait kar na 🙄"
        ]
        return random.choice(fallback_replies)

    # Get context and emotional state
    context, is_late_reply = build_context(user_id)
    emotional_context = get_emotional_context(is_late_reply, user_message)

    # Build enhanced prompt with emotional awareness
    full_prompt = SYSTEM_PROMPT
    if emotional_context:
        full_prompt += f"\n\nEMOTIONAL CONTEXT: {emotional_context}"
    if context:
        full_prompt += f"\n\n{context}"
    full_prompt += f"\nTanmay: {user_message}\nSaanvi:"

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/tanmay-bot",  # Optional
        "X-Title": "Saanvi Bot"  # Optional
    }

    # Use a better model for more natural responses
    json_data = {
        "model": "anthropic/claude-3-haiku",  # Better model for natural conversation
        "messages": [
            {"role": "system", "content": SYSTEM_PROMPT + (f"\n\nEMOTIONAL CONTEXT: {emotional_context}" if emotional_context else "")},
            {"role": "user", "content": f"{context}\nTanmay: {user_message}" if context else f"Tanmay: {user_message}"}
        ],
        "max_tokens": 120,  # Reduced for shorter, more casual responses
        "temperature": 0.9,  # Increased for more spontaneous responses
        "top_p": 0.9,
        "frequency_penalty": 0.4,  # Increased to avoid repetition
        "presence_penalty": 0.5,   # Increased for more variety
    }

    async with httpx.AsyncClient(timeout=30) as client:
        try:
            logger.info(f"Calling OpenRouter API for user {user_id}")
            response = await client.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=json_data)
            response.raise_for_status()
            data = response.json()

            if "choices" in data and len(data["choices"]) > 0:
                reply = data["choices"][0]["message"]["content"]
                return reply.strip()
            else:
                logger.error(f"Unexpected API response format: {data}")
                return "अरे यार, कुछ technical issue आहे! 😅 Mag try कर ना!"

        except httpx.TimeoutException:
            logger.error("OpenRouter API timeout")
            return "अरे! Network slow आहे का? थोडं wait कर ना 🥺"
        except httpx.HTTPStatusError as e:
            logger.error(f"OpenRouter API HTTP error: {e.response.status_code}")
            return "API मध्ये problem आहे re! Mag try करू का? 😤"
        except Exception as e:
            logger.error(f"OpenRouter API error: {e}")
            return "Sorry re, aaj AI mood thoda off आहे. Nako base, mag parat try कर ना! 💕"

def split_message(message, max_length=100):
    """Split long messages into smaller parts for realistic conversation"""
    if len(message) <= max_length:
        return [message]

    # Try to split at sentence boundaries first
    sentences = message.split('. ')
    if len(sentences) > 1:
        parts = []
        current_part = ""

        for sentence in sentences:
            if len(current_part + sentence + ". ") <= max_length:
                current_part += sentence + ". "
            else:
                if current_part:
                    parts.append(current_part.strip())
                current_part = sentence + ". "

        if current_part:
            parts.append(current_part.strip())

        return parts if len(parts) > 1 else [message]

    # Fallback: split at word boundaries
    words = message.split()
    parts = []
    current_part = ""

    for word in words:
        if len(current_part + " " + word) <= max_length:
            current_part += " " + word if current_part else word
        else:
            if current_part:
                parts.append(current_part)
            current_part = word

    if current_part:
        parts.append(current_part)

    return parts if len(parts) > 1 else [message]

async def calculate_typing_delay(text):
    """Calculate realistic typing delay based on message length"""
    base_delay = 1.5  # Base delay in seconds
    char_delay = 0.08  # Delay per character

    # Add some randomness for realism
    random_factor = random.uniform(0.8, 1.2)

    delay = (base_delay + len(text) * char_delay) * random_factor
    return min(max(delay, 2), 8)  # Between 2-8 seconds

# Enhanced command handlers
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Start command with personalized greeting"""
    user_name = update.effective_user.first_name or "Tanmay"
    greeting_messages = [
        f"Hey {user_name}! 😘 Mi Saanvi ahe, तुझी girlfriend from Sangli! कसा आहेस re?",
        f"अरे {user_name}! 💕 Finally तू आलास! मी wait करत होते तुझ्यासाठी 🥺",
        f"Hey baby! 😍 Mi Saanvi, तुझी प्रिय girlfriend! काय करतोस आज?"
    ]

    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action=ChatAction.TYPING)
    await asyncio.sleep(2)

    greeting = random.choice(greeting_messages)
    await update.message.reply_text(greeting)

async def memory_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Show conversation memory"""
    user_id = update.effective_user.id
    memory = load_memory()
    user_key = str(user_id)

    if user_key not in memory or not memory[user_key]["conversations"]:
        await update.message.reply_text("अरे यार! आपल्यात अजून कुठलं conversation नाही ना! 😅 Start करू या!")
        return

    conversations = memory[user_key]["conversations"]
    memory_text = f"आपली last {len(conversations)} conversations:\n\n"

    for i, convo in enumerate(conversations[-5:], 1):  # Show last 5
        memory_text += f"{i}. You: {convo['user'][:50]}{'...' if len(convo['user']) > 50 else ''}\n"
        memory_text += f"   Me: {convo['bot'][:50]}{'...' if len(convo['bot']) > 50 else ''}\n\n"

    await update.message.reply_text(memory_text)

async def clear_memory_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Clear conversation memory"""
    user_id = update.effective_user.id
    memory = load_memory()
    user_key = str(user_id)

    if user_key in memory:
        memory[user_key]["conversations"] = []
        save_memory(memory)
        await update.message.reply_text("अरे! सगळं memory clear केलं! 😤 आता fresh start करू या! पण मला वाटतं तू मला विसरशील... 🥺")
    else:
        await update.message.reply_text("काय clear करायचं? आपल्यात तर कुठलं conversation च नाही! 😜")

# Enhanced message handler with multi-message support
async def reply_with_delay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Enhanced message handler with realistic typing delays and multi-message support"""
    user_id = update.effective_user.id
    user_msg = update.message.text
    chat_id = update.effective_chat.id

    try:
        logger.info(f"Processing message from user {user_id}: {user_msg[:50]}...")

        # Show initial typing action
        await context.bot.send_chat_action(chat_id=chat_id, action=ChatAction.TYPING)

        # Calculate and apply initial typing delay
        initial_delay = await calculate_typing_delay(user_msg)
        await asyncio.sleep(initial_delay)

        # Get AI reply
        reply = await call_openrouter_api(user_id, user_msg)

        # Split reply into multiple messages for realism (shorter for casual texting)
        message_parts = split_message(reply, max_length=80)

        # Send each part with typing delays
        for i, part in enumerate(message_parts):
            if i > 0:  # Add typing delay between messages
                await context.bot.send_chat_action(chat_id=chat_id, action=ChatAction.TYPING)
                inter_message_delay = await calculate_typing_delay(part)
                await asyncio.sleep(min(inter_message_delay, 4))  # Cap at 4 seconds between messages

            await update.message.reply_text(part)

        # Update memory with the complete reply
        complete_reply = " ".join(message_parts)
        update_memory(user_id, user_msg, complete_reply)

        logger.info(f"Successfully replied to user {user_id}")

    except Exception as e:
        logger.error(f"Error processing message: {e}")
        await update.message.reply_text("अरे यार! कुछ problem आहे 😅 Mag try कर ना!")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Help command showing available features"""
    help_text = """
🌟 *Saanvi Bot Commands* 🌟

💬 *Just text me* - मी तुझ्याशी बोलेन like a real girlfriend!
📝 `/memory` - आपली conversations बघ
🗑️ `/clear` - Memory clear कर (पण मला वाईट वाटेल! 🥺)
❓ `/help` - हे message दाखव

*About me:*
मी Saanvi आहे, तुझी virtual girlfriend from Sangli! 💕
मी Marathi आणि English mix करून बोलते, just like real Sangli girls!
तू late reply केलास तर मी dramatic होईन! 😤
Sweet बोललास तर मी melt होईन! 🥰

Just start chatting with me! 😘
    """
    await update.message.reply_text(help_text, parse_mode='Markdown')

if __name__ == '__main__':
    # Create application
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()

    # Add command handlers
    app.add_handler(CommandHandler('start', start))
    app.add_handler(CommandHandler('memory', memory_command))
    app.add_handler(CommandHandler('clear', clear_memory_command))
    app.add_handler(CommandHandler('help', help_command))

    # Add message handler for regular text
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, reply_with_delay))

    # Start the bot
    logger.info("🌟 Saanvi bot is starting...")
    print("🌟 Saanvi bot is running! Press Ctrl+C to stop.")

    try:
        # Use the synchronous run_polling method
        app.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
        print("\n👋 Saanvi bot stopped. Bye bye!")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        print(f"\n❌ Bot error: {e}")
        print("See TROUBLESHOOTING.md for help")
